# eShowRoom

https://discord.gg/5dev

## 🚗 Système de Showroom avec Sauvegarde Persistante

Ce script permet de créer des showrooms de véhicules avec sauvegarde automatique en base de données et synchronisation en temps réel entre tous les joueurs.

### ✨ Fonctionnalités

- **Sauvegarde persistante** : Les véhicules ajoutés sont sauvegardés en base de données MySQL
- **Synchronisation temps réel** : Tous les joueurs voient les mêmes véhicules instantanément
- **Rechargement automatique** : Les véhicules réapparaissent automatiquement après un redémarrage du serveur
- **Multi-showrooms** : Support de plusieurs showrooms avec différents jobs
- **Interface intuitive** : Menu RageUI pour gérer facilement les véhicules

### 📋 Prérequis

- **ESX Framework** (Legacy ou New ESX)
- **mysql-async** ou **oxmysql**
- **RageUI** (inclus dans le script)

### 🛠️ Installation

1. **Placez le script** dans votre dossier `resources`

2. **Exécutez le script SQL** dans votre base de données :
   ```sql
   -- Copiez le contenu du fichier showroom_vehicles.sql et exécutez-le
   ```

3. **Ajoutez dans votre server.cfg** :
   ```
   ensure eShowRoom
   ```

4. **Configuration** dans `config.lua` :
   - Modifiez `Config.Framework` selon votre version d'ESX
   - Ajustez `Config.SharedObject` si nécessaire
   - Configurez `Config.Database.Enabled = true` pour activer la sauvegarde
   - Personnalisez vos showrooms (positions, jobs, etc.)

### ⚙️ Configuration

#### Base de données
```lua
Config.Database = {
    Enabled = true, -- true = sauvegarde en BDD / false = sauvegarde temporaire
    TableName = "showroom_vehicles" -- nom de la table
}
```

#### Showrooms
```lua
Config.Showrooms = {
    ["Cars"] = {
        job = "cardealer", -- Job requis pour accéder au showroom
        Menu = vector3(-573.40, -603.30, 34.72), -- Position du menu
        ExpositionPosition = {
            -- Positions des véhicules d'exposition
            {position = vector4(-588.58, -615.28, 34.72, 3.66), vehicles = {}},
            -- ... autres positions
        },
    },
}
```

### 🎮 Utilisation

1. **Accès au showroom** : Seuls les joueurs avec le bon job peuvent accéder au menu
2. **Ajouter un véhicule** : Cliquez sur un emplacement libre et entrez le nom du modèle
3. **Supprimer un véhicule** : Cliquez sur l'emplacement occupé pour supprimer le véhicule
4. **Synchronisation automatique** : Tous les changements sont visibles par tous les joueurs instantanément

### 🔧 Fonctionnement Technique

- **Côté serveur** : Gestion de la base de données et synchronisation des données
- **Côté client** : Affichage des véhicules et interface utilisateur
- **Synchronisation** : Events réseau pour maintenir la cohérence entre tous les clients

### 🐛 Dépannage

- **Les véhicules ne se sauvegardent pas** : Vérifiez que `Config.Database.Enabled = true` et que mysql-async fonctionne
- **Erreur de modèle** : Assurez-vous que le nom du véhicule est correct et que le modèle existe
- **Problème de synchronisation** : Redémarrez le script ou vérifiez les logs serveur

### 📝 Changelog

- **v1.1** : Ajout de la sauvegarde persistante en base de données
- **v1.0** : Version originale par Enøs
