-- Table pour sauvegarder les véhicules du showroom
-- Exécutez ce script dans votre base de données MySQL

CREATE TABLE IF NOT EXISTS `showroom_vehicles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `showroom_type` varchar(50) NOT NULL,
  `position_index` int(11) NOT NULL,
  `vehicle_model` varchar(100) NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_position` (`showroom_type`, `position_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
