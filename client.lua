-- Variables globales
local serverShowrooms = {} -- Données synchronisées depuis le serveur
local isUpdatingVehicles = false -- Empêcher les mises à jour multiples
local hasInitialLoad = false -- Pour éviter les spawns multiples au démarrage

if Config.Framework == "esx" then
    ESX = nil
    Citizen.CreateThread(function()
        while ESX == nil do
            TriggerEvent(Config.SharedObject, function(obj) ESX = obj end)
            Citizen.Wait(100)
        end
        ESX.PlayerData = ESX.GetPlayerData()

        -- Demander les données du serveur une fois ESX chargé
        TriggerServerEvent('showroom:requestData')
    end)
elseif Config.Framework == "newEsx" then
    ESX = exports["Framework"]:getSharedObject()
    -- Demander les données du serveur
    TriggerServerEvent('showroom:requestData')
end

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
	ESX.PlayerData = xPlayer
	-- Demander les données du serveur quand le joueur est chargé
	TriggerServerEvent('showroom:requestData')
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
	ESX.PlayerData.job = job
end)

-- Event pour recevoir les données synchronisées du serveur
RegisterNetEvent('showroom:updateAll')
AddEventHandler('showroom:updateAll', function(data)
    serverShowrooms = data

    -- Pour le premier chargement, attendre un peu plus longtemps
    if not hasInitialLoad then
        hasInitialLoad = true
        Citizen.CreateThread(function()
            Citizen.Wait(2000) -- Délai plus long pour le premier chargement
            UpdateDisplayedVehicles()
        end)
    else
        -- Mettre à jour les véhicules affichés seulement si on n'est pas déjà en train de le faire
        if not isUpdatingVehicles then
            Citizen.CreateThread(function()
                Citizen.Wait(500) -- Petit délai pour éviter les appels multiples
                UpdateDisplayedVehicles()
            end)
        end
    end
end)

function KeyboardInput(TextEntry, ExampleText, MaxStringLenght)
    AddTextEntry('FMMC_KEY_TIP1', TextEntry)
    blockinput = true
    DisplayOnscreenKeyboard(1, "FMMC_KEY_TIP1", "", ExampleText, "", "", "", MaxStringLenght)
    while UpdateOnscreenKeyboard() ~= 1 and UpdateOnscreenKeyboard() ~= 2 do 
        Wait(0)
    end 
        
    if UpdateOnscreenKeyboard() ~= 2 then
        local result = GetOnscreenKeyboardResult()
        Wait(500)
        blockinput = false
        return result
    else
        Wait(500)
        blockinput = false
        return nil
    end
end

function ShowRoom(type)
    FreezeEntityPosition(PlayerPedId(), true)
    local showroom = Config.Showrooms[type]
    local ShowroomMain = RageUI.CreateMenu("ShowRoom", type)
    ShowroomMain:SetRectangleBanner(showroom.ColorMenuR, showroom.ColorMenuG, showroom.ColorMenuB, showroom.ColorMenuA)
    RageUI.Visible(ShowroomMain, not RageUI.Visible(ShowroomMain))
    while ShowroomMain do
        Citizen.Wait(0)
        RageUI.IsVisible(ShowroomMain, true, true, true, function()

            for i = 1, #showroom.ExpositionPosition do
                local exposition = showroom.ExpositionPosition[i]
                -- Vérifier si un véhicule est présent selon les données du serveur
                local hasVehicleOnServer = serverShowrooms[type] and serverShowrooms[type][i] ~= nil

                if hasVehicleOnServer then
                    local vehicleModel = serverShowrooms[type][i]
                    RageUI.ButtonWithStyle("~r~Supprimer " .. vehicleModel, "Emplacement " .. i, {RightLabel = "→→"}, true, function(Hovered, Active, Selected)
                        if Selected then
                            SuprVehicle(type, i)
                        end
                    end)
                else
                    RageUI.ButtonWithStyle("Emplacement " .. i, nil, {RightLabel = "Libre"}, true, function(Hovered, Active, Selected)
                        if Selected then
                            local modele = KeyboardInput("Entrez le modèle", "", 100)
                            if modele and modele ~= "" then
                                SpawnVehicle(type, i, modele)
                            end
                        end
                    end)
                end
            end

        end, function()
        end)

        if not RageUI.Visible(ShowroomMain) then
            ShowroomMain = RMenu:DeleteType("ShowroomMain", true)
            FreezeEntityPosition(PlayerPedId(), false)
        end
    end
end

function SuprVehicle(type, index)
    local exposition = Config.Showrooms[type].ExpositionPosition[index]

    -- Supprimer les véhicules locaux
    while #exposition.vehicles > 0 do
        local vehicle = exposition.vehicles[1]
        ESX.Game.DeleteVehicle(vehicle)
        table.remove(exposition.vehicles, 1)
    end

    -- Informer le serveur de la suppression
    TriggerServerEvent('showroom:removeVehicle', type, index)
end

function SpawnVehicle(type, index, model)
    local showroom = Config.Showrooms[type]
    local exposition = Config.Showrooms[type].ExpositionPosition[index]

    -- Vérifier si un véhicule existe déjà à cette position
    if #exposition.vehicles > 0 then
        ESX.ShowNotification("~r~Un véhicule est déjà présent à cet emplacement!")
        return
    end

    local car = GetHashKey(model)

    if not IsModelInCdimage(car) then
        ESX.ShowNotification("~r~Modèle non valide!")
        return
    end

    RequestModel(car)
    while not HasModelLoaded(car) do
        RequestModel(car)
        Citizen.Wait(0)
    end

    -- Obtenir la position et ajuster la hauteur au sol
    local x, y, z, heading = exposition.position.x, exposition.position.y, exposition.position.z, exposition.position.w

    -- Trouver la hauteur du sol à cette position
    local groundZ = z
    local found, groundHeight = GetGroundZFor_3dCoord(x, y, z + 10.0, false)
    if found then
        groundZ = groundHeight
    end

    local vehicle = CreateVehicle(car, x, y, groundZ, heading, true, false)

    -- Attendre que le véhicule soit créé
    local timeout = 0
    while not DoesEntityExist(vehicle) and timeout < 50 do
        Citizen.Wait(100)
        timeout = timeout + 1
    end

    if DoesEntityExist(vehicle) then
        -- Placer le véhicule correctement sur le sol
        SetEntityCoordsNoOffset(vehicle, x, y, groundZ, false, false, false)
        SetEntityHeading(vehicle, heading)
        PlaceObjectOnGroundProperly(vehicle)

        FreezeEntityPosition(vehicle, true)
        SetVehicleDoorsLocked(vehicle, 4)
        SetEntityAsMissionEntity(vehicle, true, true)
        SetVehicleEngineOn(vehicle, false, true, true)
        SetVehicleUndriveable(vehicle, true)

        table.insert(exposition.vehicles, vehicle)

        if showroom.rotateVehicle then
            Citizen.CreateThread(function()
                while true do
                    if DoesEntityExist(vehicle) then
                        local currentHeading = GetEntityHeading(vehicle)
                        SetEntityHeading(vehicle, currentHeading + 1.0)
                        Citizen.Wait(30)
                    else
                        break
                    end
                end
            end)
        end

        -- Informer le serveur de l'ajout
        TriggerServerEvent('showroom:addVehicle', type, index, model)
        ESX.ShowNotification("~g~Véhicule " .. model .. " ajouté!")
    else
        ESX.ShowNotification("~r~Erreur lors de la création du véhicule!")
    end

    SetModelAsNoLongerNeeded(car)
end

-- Fonction pour mettre à jour les véhicules affichés selon les données du serveur
function UpdateDisplayedVehicles()
    if isUpdatingVehicles then return end
    isUpdatingVehicles = true

    print('[eShowRoom] Mise à jour des véhicules affichés...')

    for showroomType, showroomConfig in pairs(Config.Showrooms) do
        for i = 1, #showroomConfig.ExpositionPosition do
            local exposition = showroomConfig.ExpositionPosition[i]
            local hasVehicleOnServer = serverShowrooms[showroomType] and serverShowrooms[showroomType][i] ~= nil
            local hasVehicleLocal = #exposition.vehicles > 0

            -- Si le serveur dit qu'il y a un véhicule mais qu'il n'y en a pas localement
            if hasVehicleOnServer and not hasVehicleLocal then
                local model = serverShowrooms[showroomType][i]
                print('[eShowRoom] Spawn véhicule: ' .. model .. ' à la position ' .. i)
                SpawnVehicleLocal(showroomType, i, model)
            -- Si le serveur dit qu'il n'y a pas de véhicule mais qu'il y en a un localement
            elseif not hasVehicleOnServer and hasVehicleLocal then
                print('[eShowRoom] Suppression véhicule local à la position ' .. i)
                RemoveVehicleLocal(showroomType, i)
            end
        end
    end

    isUpdatingVehicles = false
end

-- Fonction pour spawn un véhicule localement (sans informer le serveur)
function SpawnVehicleLocal(type, index, model)
    local showroom = Config.Showrooms[type]
    local exposition = Config.Showrooms[type].ExpositionPosition[index]

    local car = GetHashKey(model)

    if not IsModelInCdimage(car) then
        print('[eShowRoom] Modèle invalide: ' .. model)
        return
    end

    RequestModel(car)
    while not HasModelLoaded(car) do
        RequestModel(car)
        Citizen.Wait(0)
    end

    -- Obtenir la position et ajuster la hauteur au sol
    local x, y, z, heading = exposition.position.x, exposition.position.y, exposition.position.z, exposition.position.w

    -- Trouver la hauteur du sol à cette position
    local groundZ = z
    local found, groundHeight = GetGroundZFor_3dCoord(x, y, z + 10.0, false)
    if found then
        groundZ = groundHeight
    end

    local vehicle = CreateVehicle(car, x, y, groundZ, heading, true, false)

    -- Attendre que le véhicule soit créé
    local timeout = 0
    while not DoesEntityExist(vehicle) and timeout < 50 do
        Citizen.Wait(100)
        timeout = timeout + 1
    end

    if DoesEntityExist(vehicle) then
        -- Placer le véhicule correctement sur le sol
        SetEntityCoordsNoOffset(vehicle, x, y, groundZ, false, false, false)
        SetEntityHeading(vehicle, heading)
        PlaceObjectOnGroundProperly(vehicle)

        FreezeEntityPosition(vehicle, true)
        SetVehicleDoorsLocked(vehicle, 4)
        SetEntityAsMissionEntity(vehicle, true, true)
        SetVehicleEngineOn(vehicle, false, true, true)
        SetVehicleUndriveable(vehicle, true)

        table.insert(exposition.vehicles, vehicle)

        if showroom.rotateVehicle then
            Citizen.CreateThread(function()
                while true do
                    if DoesEntityExist(vehicle) then
                        local currentHeading = GetEntityHeading(vehicle)
                        SetEntityHeading(vehicle, currentHeading + 1.0)
                        Citizen.Wait(30)
                    else
                        break
                    end
                end
            end)
        end

        print('[eShowRoom] Véhicule ' .. model .. ' spawné à la position ' .. index)
    else
        print('[eShowRoom] Échec du spawn du véhicule ' .. model)
    end

    SetModelAsNoLongerNeeded(car)
end

-- Fonction pour supprimer un véhicule localement (sans informer le serveur)
function RemoveVehicleLocal(type, index)
    local exposition = Config.Showrooms[type].ExpositionPosition[index]

    while #exposition.vehicles > 0 do
        local vehicle = exposition.vehicles[1]
        ESX.Game.DeleteVehicle(vehicle)
        table.remove(exposition.vehicles, 1)
    end
end

Citizen.CreateThread(function()
    while true do
        local _Wait = 500
        local PlyID = GetPlayerPed(-1)
        local PlayerCoords = GetEntityCoords(PlyID, false)
        for showroomType, showroomConfig in pairs(Config.Showrooms) do
            if ESX.PlayerData.job and ESX.PlayerData.job.name == showroomConfig.job then
                local Dist = Vdist(PlayerCoords.x, PlayerCoords.y, PlayerCoords.z, showroomConfig.Menu.x, showroomConfig.Menu.y, showroomConfig.Menu.z)
                if Dist <= showroomConfig.Marker.DrawDistance then
                    _Wait = 0
                    DrawMarker(showroomConfig.Marker.Type, showroomConfig.Menu.x, showroomConfig.Menu.y, showroomConfig.Menu.z-0.99, nil, nil, nil, -90, nil, nil, showroomConfig.Marker.Size.x, showroomConfig.Marker.Size.y, showroomConfig.Marker.Size.z, showroomConfig.Marker.Color.R, showroomConfig.Marker.Color.G, showroomConfig.Marker.Color.B, showroomConfig.Marker.Color.A)
                end
                if Dist <= showroomConfig.Marker.DrawInteract then
                    _Wait = 0
                    ESX.ShowHelpNotification("Appuyez sur ~INPUT_CONTEXT~ pour ouvrir le showroom")
                    if IsControlJustPressed(1,51) then
                        TypeShow = showroomType
                        ShowRoom(TypeShow)
                    end
                end
            end
        end
        Citizen.Wait(_Wait)   
    end
end)