local showrooms = {}

-- Fonction pour charger les véhicules depuis la base de données
function LoadShowroomVehicles()
    if Config.Database.Enabled then
        MySQL.Async.fetchAll('SELECT * FROM ' .. Config.Database.TableName, {}, function(result)
            showrooms = {}
            for i = 1, #result do
                local row = result[i]
                if not showrooms[row.showroom_type] then
                    showrooms[row.showroom_type] = {}
                end
                showrooms[row.showroom_type][row.position_index] = row.vehicle_model
            end
            print('[eShowRoom] Chargé ' .. #result .. ' véhicules depuis la base de données')
        end)
    end
end

-- Fonction pour sauvegarder un véhicule en base de données
function SaveVehicleToDatabase(type, index, model)
    if Config.Database.Enabled then
        MySQL.Async.execute('INSERT INTO ' .. Config.Database.TableName .. ' (showroom_type, position_index, vehicle_model) VALUES (@type, @index, @model) ON DUPLICATE KEY UPDATE vehicle_model = @model', {
            ['@type'] = type,
            ['@index'] = index,
            ['@model'] = model
        }, function(affectedRows)
            if affectedRows > 0 then
                print('[eShowRoom] Véhicule sauvegardé: ' .. model .. ' à la position ' .. index .. ' du showroom ' .. type)
            end
        end)
    end
end

-- Fonction pour supprimer un véhicule de la base de données
function RemoveVehicleFromDatabase(type, index)
    if Config.Database.Enabled then
        MySQL.Async.execute('DELETE FROM ' .. Config.Database.TableName .. ' WHERE showroom_type = @type AND position_index = @index', {
            ['@type'] = type,
            ['@index'] = index
        }, function(affectedRows)
            if affectedRows > 0 then
                print('[eShowRoom] Véhicule supprimé de la position ' .. index .. ' du showroom ' .. type)
            end
        end)
    end
end

RegisterServerEvent('showroom:addVehicle')
AddEventHandler('showroom:addVehicle', function(type, index, model)
    if not showrooms[type] then showrooms[type] = {} end
    showrooms[type][index] = model

    -- Sauvegarder en base de données
    SaveVehicleToDatabase(type, index, model)

    -- Synchroniser avec tous les clients
    TriggerClientEvent('showroom:updateAll', -1, showrooms)
end)

RegisterServerEvent('showroom:removeVehicle')
AddEventHandler('showroom:removeVehicle', function(type, index)
    if showrooms[type] then
        showrooms[type][index] = nil

        -- Supprimer de la base de données
        RemoveVehicleFromDatabase(type, index)

        -- Synchroniser avec tous les clients
        TriggerClientEvent('showroom:updateAll', -1, showrooms)
    end
end)

RegisterNetEvent('showroom:requestData')
AddEventHandler('showroom:requestData', function()
    local src = source
    TriggerClientEvent('showroom:updateAll', src, showrooms)
end)

-- Charger les véhicules au démarrage du serveur
Citizen.CreateThread(function()
    Citizen.Wait(1000) -- Attendre que MySQL soit prêt
    LoadShowroomVehicles()
end)
