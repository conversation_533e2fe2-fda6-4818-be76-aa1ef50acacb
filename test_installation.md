# 🧪 Guide de Test - eShowRoom

## Étapes de test pour vérifier le bon fonctionnement

### 1. Vérification de l'installation

1. **Vérifiez que la table existe** :
   ```sql
   SHOW TABLES LIKE 'showroom_vehicles';
   ```

2. **Vérifiez que le script démarre sans erreur** :
   - Regardez les logs du serveur au démarrage
   - Vous devriez voir : `[eShowRoom] Chargé X véhicules depuis la base de données`

### 2. Test de base

1. **Connectez-vous avec un personnage ayant le job `cardealer`**
2. **Allez à la position du menu** : `-573.40, -603.30, 34.72`
3. **Ouvrez le menu** avec la touche E
4. **Ajoutez un véhicule** :
   - Cliquez sur "Emplacement 1"
   - Entrez un modèle valide (ex: `adder`, `zentorno`, `t20`)
   - Le véhicule devrait apparaître

### 3. Test de persistance

1. **Ajoutez plusieurs véhicules** dans différents emplacements
2. **Redémarrez le serveur** : `restart eShowRoom`
3. **Reconnectez-vous** et vérifiez que :
   - Les véhicules sont toujours présents
   - Ils sont aux bonnes positions
   - Le menu affiche correctement les emplacements occupés

### 4. Test de synchronisation

1. **Avec un premier joueur** : Ajoutez un véhicule
2. **Avec un second joueur** (même job) :
   - Allez au showroom
   - Vérifiez que le véhicule est visible
   - Vérifiez que l'emplacement apparaît comme occupé dans le menu
3. **Supprimez le véhicule** avec le second joueur
4. **Vérifiez avec le premier joueur** que le véhicule a disparu

### 5. Vérification en base de données

```sql
-- Voir tous les véhicules sauvegardés
SELECT * FROM showroom_vehicles;

-- Voir les véhicules d'un showroom spécifique
SELECT * FROM showroom_vehicles WHERE showroom_type = 'Cars';
```

### 6. Tests d'erreur

1. **Modèle invalide** : Entrez un nom de véhicule qui n'existe pas
   - Vous devriez voir : "Modèle non valide!"
2. **Job incorrect** : Connectez-vous avec un autre job
   - Le marker ne devrait pas apparaître
3. **Emplacement occupé** : Essayez d'ajouter un véhicule sur un emplacement déjà pris
   - L'option devrait afficher "Supprimer" au lieu de "Libre"

### ✅ Résultats attendus

- ✅ Les véhicules persistent après redémarrage
- ✅ La synchronisation fonctionne entre joueurs
- ✅ Les emplacements libres/occupés sont correctement affichés
- ✅ Seuls les joueurs avec le bon job peuvent accéder
- ✅ Les données sont sauvegardées en base de données

### 🚨 Problèmes courants

- **Véhicules qui ne persistent pas** : Vérifiez mysql-async et la configuration de la BDD
- **Pas de synchronisation** : Vérifiez les events réseau dans les logs
- **Menu qui ne s'ouvre pas** : Vérifiez le job du joueur et la position du marker
